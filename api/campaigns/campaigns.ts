import { NextApiRequest, NextApiResponse } from 'next';
import { createSupabaseServerClient } from '../utils/supabase';
import type { CampaignRow } from '@/types/campaigns';
import { sortCampaignsByFunding } from '../utils/campaign-sorting';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<CampaignRow[] | { message: string; error?: string }>
) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const supabase = await createSupabaseServerClient();

    // Fetch all active campaigns
    const { data: campaigns, error } = await supabase
      .from('campaigns')
      .select('*')
      .eq('active', true);

    if (error) {
      console.error('Supabase error:', error);
      return res.status(500).json({ message: 'Error fetching campaigns', error: error.message });
    }

    if (!campaigns) {
      return res.status(200).json([]);
    }

    // Sort campaigns by UPD funding amount (highest first), then by ID (lowest first)
    const sortedCampaigns = sortCampaignsByFunding(campaigns);

    res.status(200).json(sortedCampaigns);
  } catch (error: any) { // Catch as any or unknown and refine
    console.error('API route error:', error);
    res.status(500).json({ message: 'Internal Server Error', error: error.message });
  }
}